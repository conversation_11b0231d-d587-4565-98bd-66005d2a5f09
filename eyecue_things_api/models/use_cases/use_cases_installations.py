from uuid import UUID, uuid4

from pydantic import (
    EmailStr,
    Field,
)

from eyecue_things_api.models.types import SiteId
from eyecue_things_api.models.use_cases.base import RecordEntityBase
from eyecue_things_api.models.use_cases.types import (
    CameraRoiInfo,
    UseCaseInstallationId,
)


class UseCaseInstallation(RecordEntityBase):
    site_id: SiteId = Field(
        description="The site_id of the installation.",
    )
    installation_id: UseCaseInstallationId = Field(
        description="The use case and installation UUIDs combined.",
        examples=["usecase#<uuid>|installation#<uuid>"],
    )
    cameras: list[CameraRoiInfo] = Field(
        description="The cameras and rois that are linked to the installation.",
    )
    author_name: EmailStr = Field(
        description="The author's email used to create/modify the installation.",
        examples=["<EMAIL>"],
    )

    @classmethod
    def create_installation(
        cls,
        site_id: SiteId,
        use_case_id: UUID,
        cameras: list[CameraRoiInfo],
        author_name: EmailStr,
    ) -> "UseCaseInstallation":
        installation_uuid = uuid4()
        installation_id = f"usecase#{use_case_id}|installation#{installation_uuid}"
        return cls(
            site_id=site_id,
            installation_id=installation_id,
            cameras=cameras,
            author_name=author_name,
        )
