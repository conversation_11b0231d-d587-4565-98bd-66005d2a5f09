import uuid as uuid_lib
from typing import Annotated
from uuid import UUID

from pydantic import (
    AfterValidator,
    BaseModel,
    EmailStr,
    Field,
)

from eyecue_things_api.models.discriminator import TrackerRoi
from eyecue_things_api.models.types import OnetoFiveDigitStr, SiteId

UUID_VERSION = 4


def validate_usecase_installation_id(value: str) -> str:
    try:
        usecase_part, installation_part = value.split("|")
        prefix1, uuid_str1 = usecase_part.split("#", 1)
        prefix2, uuid_str2 = installation_part.split("#", 1)
    except ValueError as verr:
        raise ValueError(
            "Value must be in format 'usecase#<uuid>|installation#<uuid>'"
        ) from verr

    if prefix1 != "usecase" or prefix2 != "installation":
        raise ValueError("Prefixes must be 'usecase' and 'installation' respectively")

    for label, uuid_str in [(prefix1, uuid_str1), (prefix2, uuid_str2)]:
        try:
            uuid = UUID(uuid_str)
        except ValueError as verr:
            raise ValueError(f"{label} value is not a valid UUID") from verr

        if uuid.version != UUID_VERSION:
            raise ValueError(f"{label} UUID is not version 4")

    return value


UseCaseInstallationId = Annotated[str, AfterValidator(validate_usecase_installation_id)]


class CameraRoiInfo(BaseModel):
    id: OnetoFiveDigitStr = Field(
        description="The camera_id of the RoI using only numbers.",
        examples=["015"],
    )
    roi_ids: list[UUID] = Field(
        description="A list of RoIs UUIDs.",
        examples=["abbbfecc-e66c-449c-bcaa-a2d1516f8638"],
    )


class InstallationCamera(BaseModel):
    id: OnetoFiveDigitStr = Field(
        description="The camera_id of the RoIs, inform only numbers.",
        examples=["015"],
    )
    rois: list[TrackerRoi] = Field(
        description="The RoIs for the camera",
    )


class UseCaseInstallationProvision(BaseModel):
    site_id: SiteId = Field(
        description="The site_id of the installation.",
    )
    use_case_id: uuid_lib.UUID = Field(
        description="UUID for the ROI",
    )
    cameras: list[InstallationCamera] = Field(
        description="List of InstallationCamera objects,\
        containing camera_ids and RoI objects."
    )
    author_name: EmailStr = Field(
        description="The author's email used to create/modify the installation.",
        examples=["<EMAIL>"],
    )
