import uuid as uuid_lib

from pydantic import Field, HttpUrl

from eyecue_things_api.models.types import UseCaseNames, UseCaseStatus
from eyecue_things_api.models.use_cases.base import RecordEntityBase


class UseCase(RecordEntityBase):
    use_case_id: uuid_lib.UUID = Field(
        description="UUID for the use case",
    )
    name: str | None = Field(
        description="The user-friendly name of the use case.",
        examples=["Idle Time"],
    )
    category: str = Field(
        description="The category of the use case.",
        examples=["Front Counter"],
    )
    description: str = Field(
        description="The description of the use case.",
    )
    status: UseCaseStatus = Field(
        description="The status of the use case.", examples=["Available"]
    )
    tags: list[str] | None = Field(
        description="The tags that represent/categorize the use case.",
    )
    image_url: HttpUrl | None = Field(
        description="The URL of the use case image.",
    )
    thumbnail_url: HttpUrl = Field(
        description="The URL of the thumbnail of use case image.",
    )
    schema_name: UseCaseNames = Field(
        description="The schema name for the use case.", examples=["workstation_stats"]
    )
    version: str = Field(
        description="The version of the use case.", examples=["1.3"], default="1.0"
    )
    edge_support: bool = Field(
        description="Whether the model can be installed on the \
        edge or only on the cloud.",
        default=True,
    )
