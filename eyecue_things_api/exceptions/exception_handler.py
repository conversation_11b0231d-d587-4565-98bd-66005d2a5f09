import json
from http import HTTPStatus

from aws_lambda_powertools.event_handler.api_gateway import (
    APIGatewayRestResolver,
    Response,
)

from eyecue_things_api.exceptions.errors import (
    HANDLED_ERRORS,
    ModelValidationError,
    ServiceError,
)


def map_exception_to_http_error(exception: Exception) -> ServiceError:
    # If already a ServiceError, return as is
    if isinstance(exception, ServiceError):
        return exception

    return ModelValidationError(f"Model validation error: {exception!s}")


def register_exception_handlers(app: APIGatewayRestResolver) -> None:
    for exception_type in HANDLED_ERRORS:
        # Define a handler function inside the loop
        def handler(
            e: ServiceError, *, status: HTTPStatus = exception_type.status_code
        ) -> Response:
            return Response(
                status_code=status.value,
                content_type="application/json",
                body=json.dumps({"message": str(e)}),
            )

        # Register the handler with the app using decorator syntax
        app.exception_handler(exception_type)(handler)


    @app.not_found
    def handle_not_found(e: Exception) -> Response:
        """Handle 404 Not Found errors for unmatched routes."""
        return Response(
            status_code=HTTPStatus.NOT_FOUND.value,
            content_type="application/json",
            body=json.dumps({
                "message": "Not Found",
                "error": HTTPStatus.NOT_FOUND.phrase
            }),
        )

    @app.exception_handler(Exception)
    def fallback_handler(e: Exception) -> Response:
        mapped = map_exception_to_http_error(e)  # <- your mapping logic
        return Response(
            status_code=mapped.status_code.value,
            content_type="application/json",
            body=json.dumps({"message": str(mapped)}),
        )
