import json
from http import HTTPStatus

from aws_lambda_powertools import Logger
from aws_lambda_powertools.event_handler import APIGatewayRestResolver, Response
from aws_lambda_powertools.logging import correlation_paths
from aws_lambda_powertools.utilities.typing import LambdaContext

from eyecue_things_api.context import Context
from eyecue_things_api.exceptions.exception_handler import register_exception_handlers
from eyecue_things_api.routes.router import router
from eyecue_things_api.util import add_cors_headers

logger = Logger()

app = APIGatewayRestResolver(enable_validation=True, strip_prefixes=["/api/v1"])
register_exception_handlers(app)
app.include_router(router)


@logger.inject_lambda_context(correlation_id_path=correlation_paths.API_GATEWAY_REST)
def lambda_handler(
    event: dict, context: LambdaContext, app_context: Context | None = None
) -> dict:
    """Handle the routing of events from the API Gateway to the functions.

    Args:
        event (dict): Event data passed to the Lambda function
        context (LambdaContext): Lambda context object
        app_context (Context | None): Application context (only used for testing).

    Returns:
        dict: Response data to be returned to the API Gateway.

    """
    if app_context is None:
        app_context = Context()

    app.append_context(
        lambda_context=context,
        app_context=app_context,
    )
    response = app.resolve(event, context)

    return add_cors_headers(response)


if __name__ == "__main__":

    class MockContext:
        def __init__(self):
            self.function_name = "my_lambda_function"
            self.function_version = "$LATEST"
            self.invoked_function_arn = (
                "arn:aws:lambda:us-east-1:************:function:my_lambda_function"
            )
            self.memory_limit_in_mb = "128"
            self.aws_request_id = "mock-request-id"
            self.log_group_name = "/aws/lambda/my_lambda_function"
            self.log_stream_name = "2025/08/07/[$LATEST]abcdef123456abcdef"

    event = {
        "httpMethod": "PUT",
        "path": "/api/v1/installations/usecase%23",
        "headers": {
            "Content-Type": "application/json",
        },
        "queryStringParameters": {},
        "pathParameters": "",
        "body": json.dumps(
            {
                "last_updated_timestamp": "2025-08-08T03:49:50.564368+00:00",
                "site_id": "fm-kfc-aus-67441",
                "installation_id": "usecase#6ddd42cd-9712-4cb0-9726-0bb2f16f6fa5|installation#39fe8ec2-d8e8-42df-a5f0-4aa5e22d86f6",
                "rois": [],
                "author_name": "<EMAIL>",
            }
        ),
        "isBase64Encoded": False,
    }
    print(lambda_handler(event, context=MockContext()))
