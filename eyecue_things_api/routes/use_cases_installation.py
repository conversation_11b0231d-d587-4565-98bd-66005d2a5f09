from http import HTTPStatus
from typing import TYPE_CHECKING, cast

from aws_lambda_powertools.event_handler import Response
from aws_lambda_powertools.event_handler.router import APIGatewayRouter

from eyecue_things_api.context import Context
from eyecue_things_api.response import SuccessResponse
from eyecue_things_api.services.tracker.use_cases_installation import (
    UseCasesInstallationService,
)

if TYPE_CHECKING:
    from aws_lambda_powertools.event_handler.openapi.types import OpenAPIResponse
from typing import TYPE_CHECKING, Annotated

from aws_lambda_powertools.event_handler.openapi.params import Body, Path, Query

from eyecue_things_api.models.use_cases.types import UseCaseInstallationProvision
from eyecue_things_api.models.use_cases.use_cases_installations import (
    UseCaseInstallation,
)

router = APIGatewayRouter()
use_cases_installation_service = UseCasesInstallationService(Context())

responses = cast(
    "dict[int, OpenAPIResponse]",
    {
        HTTPStatus.OK: {"description": "Success"},
        HTTPStatus.NOT_FOUND: {"description": "Not Found"},
        HTTPStatus.FORBIDDEN: {"description": "Forbidden"},
        HTTPStatus.INTERNAL_SERVER_ERROR: {"description": "Server Error"},
    },
)

# TODO: Check if organisation wide installation endpoint is required
@router.get(
    rule="/org/<organisation>/installations",
    tags=["Use Cases Installations"],
    summary="Get list of all available use cases installations for a given organisation.",
    operation_id="getAllUseCaseInstallations",
    responses=responses,
)
def get_all_use_case_installations(
    organisation: Annotated[str, Path(description="The organisation id.")],
) -> Response[list[UseCaseInstallation]]:
    use_case_installations = (
        use_cases_installation_service.get_all_use_cases_installations()
    )
    return SuccessResponse[list[UseCaseInstallation]](use_case_installations)

@router.get(
    rule="/org/<organisation>/sites/<site_id>/installations",
    tags=["Use Cases Installations"],
    summary="Get list of all available use cases installations \
        filtered by site id and use case id (optional).",
    operation_id="getUseCaseInstallations",
    responses=responses,
)
def get_all_use_case_installations_filtered(
    organisation: Annotated[str, Path(description="The organisation id.")],
    site_id: Annotated[
        str,
        Path(
            description="Filter use case installations by \
                                 the site id of the store."
        ),
    ],
    use_case_id: Annotated[
        str | None,
        Query(
            description="(Optional) Filter use case installation by\
                the use_case_id (UUID).",
            examples=["d5fbdf86-4dd0-4bf8-9932-be4e1aac5f44"],
        ),
    ] = None,
) -> Response[list[UseCaseInstallation]]:
    use_case_installations = (
        use_cases_installation_service.get_all_use_case_installations_filtered(
            site_id=site_id, use_case_id=use_case_id
        )
    )
    return SuccessResponse[list[UseCaseInstallation]](use_case_installations)


@router.post(
    rule="/org/<organisation>/sites/<site_id>/installations",
    tags=["Use Cases Installations"],
    summary="Create a new use case installation",
    operation_id="createUseCaseInstallation",
    responses=responses,
)
def create_use_case_installation(
    organisation: Annotated[str, Path(description="The organisation id.")],
    site_id: Annotated[str, Path(description="The site id of the store.")],
    use_case: Annotated[
        UseCaseInstallation, Body(description="Use case installation payload.")
    ],
) -> Response[UseCaseInstallation]:
    use_case_installations = use_cases_installation_service.create(use_case)
    return SuccessResponse[UseCaseInstallation](use_case_installations)


@router.post(
    rule="/org/<organisation>/sites/<site_id>/installations/provision",
    tags=["Use Cases Installations"],
    summary="Provision a new use case installation and create all of its dependencies.",
    operation_id="provisionUseCaseInstallation",
    responses=responses,
)
def provision_use_case_installation(
    organisation: Annotated[str, Path(description="The organisation id.")],
    site_id: Annotated[str, Path(description="The site id of the store.")],
    use_case_installation_provision: Annotated[
        UseCaseInstallationProvision, Body(description="Use case installation payload.")
    ],
) -> Response[UseCaseInstallation]:
    use_case_installation = use_cases_installation_service.provision(
        use_case_installation_provision
    )
    return SuccessResponse[UseCaseInstallation](use_case_installation)


@router.put(
    rule="/org/<organisation>/sites/<site_id>/installations/<installation_id>",
    tags=["Use Cases Installations"],
    summary="Update an existing use case installation",
    operation_id="updateUseCaseInstallation",
    responses=responses,
)
def update_use_case_installation(
    organisation: Annotated[str, Path(description="The organisation id.")],
    site_id: Annotated[str, Path(description="The site id of the store.")],
    installation_id: Annotated[str, Path(description="The use case installation id.")],
    use_case_installation: Annotated[
        UseCaseInstallation, Body(description="Use case installation payload.")
    ],
) -> Response[UseCaseInstallation]:
    updated_use_case_installation = use_cases_installation_service.update(
        installation_id, use_case_installation
    )
    return SuccessResponse[UseCaseInstallation](updated_use_case_installation)


@router.delete(
    rule="/org/<organisation>/sites/<site_id>/installations/<installation_id>",
    tags=["Use Cases Installations"],
    summary="Delete an existing use case installation",
    operation_id="deleteUseCaseInstallation",
    responses=responses,
)
def delete_use_case_installation(
    organisation: Annotated[str, Path(description="The organisation id.")],
    site_id: Annotated[str, Path(description="The site id of the store.")],
    installation_id: Annotated[str, Path(description="The use case installation id.")],
) -> Response[None]:
    use_cases_installation_service.delete(site_id, installation_id)
    return SuccessResponse[None](None)
