from contextlib import suppress

from eyecue_things_api.models.use_cases.types import (
    CameraRoiInfo,
    UseCaseInstallationProvision,
)
from eyecue_things_api.services.tracker.rois import RoiService


def rollback_rois_creation(
    installation_provision: UseCaseInstallationProvision,
    provisioned_rois: list[CameraRoiInfo],
    roi_service: RoiService,
) -> None:
    """Rollback the RoIs' Creation (Delete them) if something goes wrong.

    We suppress the Exceptions as we don't care if one operation fails, we need
    to try to revert as many as possible.

    Args:
        installation_provision (UseCaseInstallationProvision): Contains information
            about site_id, camera_id and the data for the RoIs to be created.
        provisioned_rois: list[CameraRoiInfo]: Keeps track of created RoIs.
        roi_service (RoiService): An instantiated RoiService class.

    """
    for camera in provisioned_rois:
        with suppress(Exception):
            roi_service.delete(
                installation_provision.site_id,
                camera.id,
                list(map(str, camera.roi_ids)),
            )


def provision_rois(
    installation_provision: UseCaseInstallationProvision, roi_service: RoiService
) -> tuple[list[CameraRoiInfo], bool]:
    """Provision (Create) RoIs on each Camera and returns the RoI UUID with Camera ID.

    Args:
        installation_provision (UseCaseInstallationProvision): Contains information
            about site_id, camera_id and the data for the RoIs to be created.
        roi_service (RoiService): An instantiated RoiService class.

    Returns:
        list[CameraRoiInfo]: Returns the created RoIs in the CameraRoiInfo format.
        bool: Informs if there were any issues when creating the RoIs.

    """
    cameras_roi_info: list[CameraRoiInfo] = []
    for camera in installation_provision.cameras:
        try:
            roi_service.create(installation_provision.site_id, camera.id, camera.rois)
            rois_uuids = [roi.uuid for roi in camera.rois]
            cameras_roi_info.append(CameraRoiInfo(id=camera.id, roi_ids=rois_uuids))
        except Exception:  # noqa: BLE001
            return cameras_roi_info, False
    return cameras_roi_info, True
