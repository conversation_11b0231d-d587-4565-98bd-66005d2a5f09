from aws_lambda_powertools import Logger
from aws_lambda_powertools.utilities.typing import LambdaContext
from aws_lambda_powertools.utilities.data_classes import event_source
from aws_lambda_powertools.utilities.data_classes.api_gateway_authorizer_event import APIGatewayAuthorizerRequestEvent

from functions.auth.src.config import Config
from functions.auth.src.jwt import J<PERSON><PERSON>uthorizer, JWTClaims
from functions.auth.src.util import split_method_arn, generate_policy

logger = Logger()
config = Config()


def _build_allowed_resources(method_arn: str, claims: JWTClaims) -> list[str]:
    """Build a list of allowed execute-api ARNs for organisations and sites."""
    arn_base, stage, verb, resource_path = split_method_arn(method_arn)

    allowed_resources: list[str] = []

    # Determine allowed organisations from claims
    sites_map = claims.app_metadata.iq_data['v1']['full-access:dashboard'] or {}
    for org, site_list in sites_map.items():

        # Site-level endpoints
        if "*" in site_list:
            allowed_resources.append(f"{arn_base}/*/*/api/v1/org/{org}/sites/*")
        else:
            for site_id in site_list:
                allowed_resources.append(f"{arn_base}/*/*/api/v1/org/{org}/sites/{site_id}/*")

        # Org-level endpoints
        allowed_resources.append(f"{arn_base}/*/*/api/v1/org/{org}/schemas/*")
        allowed_resources.append(f"{arn_base}/*/*/api/v1/org/{org}/installations")
        allowed_resources.append(f"{arn_base}/*/*/api/v1/org/{org}/installations/*")
        allowed_resources.append(f"{arn_base}/*/GET/api/v1/org/{org}/use-cases") # Org-level use cases are read-only

        # Administrator endpoints
        if config.admin_role in claims.roles:
            # Only admins can POST, PUT and DELETE this endpoint but everyone can GET
            allowed_resources.append(f"{arn_base}/*/POST/api/v1/org/{org}/use-cases")
            allowed_resources.append(f"{arn_base}/*/PUT/api/v1/org/{org}/use-cases/*")
            allowed_resources.append(f"{arn_base}/*/DELETE/api/v1/org/{org}/use-cases/*")

    return allowed_resources


@logger.inject_lambda_context
@event_source(data_class=APIGatewayAuthorizerRequestEvent)
def lambda_handler(event: APIGatewayAuthorizerRequestEvent, context: LambdaContext) -> dict | None:
    """Lambda handler for HTTP API (v2) request authorizer."""
    try:
        # Extract token from common event shapes
        token = event.authorization_token.replace("Bearer ", "").strip()

        if not token:
            logger.error("No authorization token provided")
            raise ValueError("No authorization token provided")

        if not event.method_arn:
            logger.error("No route/method ARN provided")
            raise ValueError("No route/method ARN provided")

        # Initialize authorizer
        authorizer = JWTAuthorizer(issuer=config.jwt_domain, audience=config.jwt_audience)

        # Validate token and extract claims
        claims = authorizer.validate_token(token)

        # Build list of allowed resources based on organisations + sites in claims
        allowed_resources = _build_allowed_resources(event.method_arn, claims)
        if not allowed_resources:
            policy = generate_policy("unauthorized", "Deny", event.method_arn)
        else:
            policy = generate_policy(claims.sub, "Allow", allowed_resources, claims.model_dump())

        logger.info("Authorization result", extra={"policy": policy})
        return policy

    except Exception as e:
        logger.error("Authorization failed", extra={"error": str(e)})
        return generate_policy("unauthorized", "Deny", event.method_arn)
