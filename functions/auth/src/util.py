def split_method_arn(method_arn: str) -> tuple[str, str, str, str]:
    """Split methodArn into base ARN, stage, verb and resource path."""
    arn_parts = method_arn.split("/")
    if len(arn_parts) < 2:
        raise ValueError("Unexpected methodArn format")

    arn_base = arn_parts[0]  # arn:aws:execute-api:region:account:apiId
    stage = arn_parts[1]
    verb = arn_parts[2]
    resource_path = "/".join(arn_parts[3:])
    return arn_base, stage, verb, resource_path


def generate_policy(principal_id: str, effect: str, resources: list | str,
                    context: dict | None = None) -> dict:
    """Generate IAM policy for API Gateway custom authorizer.

    Resource may be a single ARN string or a list of ARNs.
    """
    policy = {
        "principalId": principal_id,
        "policyDocument": {
            "Version": "2012-10-17",
            "Statement": [
                {
                    "Action": "execute-api:Invoke",
                    "Effect": effect,
                    "Resource": resources,
                }
            ]
        }
    }

    if context:
        policy["context"] = {k: str(v) for k, v in context.items()}

    return policy
