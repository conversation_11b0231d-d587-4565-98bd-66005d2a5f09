components:
  schemas:
    Camera:
      properties:
        FPS:
          anyOf:
          - minimum: 1.0
            type: integer
          - type: 'null'
          default: 6
          description: Max frames per second that the camera should track.
          title: Fps
        camera_id:
          description: The ID of the camera (must be a one to five-digit number).
          examples:
          - '16'
          - 00148
          pattern: ^\d{1,5}$
          title: Camera Id
          type: string
        finish_camera:
          anyOf:
          - type: boolean
          - type: 'null'
          default: false
          description: Whether this is a finish camera or not.
          title: Finish Camera
        main_flow_direction:
          anyOf:
          - type: string
          - type: 'null'
          deprecated: true
          description: The direction the vehicles are moving in the main flow of traffic.
            (Deprecated)
          title: Main Flow Direction
        name:
          anyOf:
          - type: string
          - type: 'null'
          description: Name of the camera.
          examples:
          - fm-mcd-aus-1305-016
          - fm-cfa-usa-01490-148
          title: Name
        reverse_detection:
          anyOf:
          - type: boolean
          - type: 'null'
          default: false
          description: Whether to detect reverse or not.
          title: Reverse Detection
        reverse_frames_threshold:
          anyOf:
          - minimum: 1.0
            type: integer
          - type: 'null'
          default: 3
          description: How many frames a car must be reversing to trigger a reverse
            status
          title: Reverse Frames Threshold
        s3_path:
          anyOf:
          - type: string
          - type: 'null'
          description: Path to the s3 bucket image of this camera.
          title: S3 Path
        source:
          anyOf:
          - format: uri
            minLength: 1
            type: string
          - format: path
            type: string
          description: RTPS Source of the camera.
          examples:
          - rtsp://localhost:8554/0
          - video.mp4
          title: Source
        start_camera:
          anyOf:
          - type: boolean
          - type: 'null'
          default: false
          description: Whether this is a start camera or not.
          title: Start Camera
      required:
      - camera_id
      - source
      title: Camera
      type: object
    CameraRoiInfo:
      properties:
        id:
          description: The camera_id of the RoI using only numbers.
          examples:
          - '015'
          pattern: ^\d{1,5}$
          title: Id
          type: string
        roi_ids:
          description: A list of RoIs UUIDs.
          examples:
          - abbbfecc-e66c-449c-bcaa-a2d1516f8638
          items:
            format: uuid
            type: string
          title: Roi Ids
          type: array
      required:
      - id
      - roi_ids
      title: CameraRoiInfo
      type: object
    ClassName:
      enum:
      - car
      - motorcycle
      - person
      - trailer
      - truck
      title: ClassName
      type: string
    Classes:
      properties:
        max_age:
          anyOf:
          - minimum: 1.0
            type: integer
          - type: 'null'
          default: 6
          description: Maximum age of the asset (in seconds) before it gets deleted
            if it is not updated.
          title: Max Age
        min_hits:
          anyOf:
          - minimum: 1.0
            type: integer
          - type: 'null'
          default: 3
          description: Minimum number of hits before the asset is considered valid.
          title: Min Hits
        name:
          anyOf:
          - $ref: '#/components/schemas/ClassName'
          - type: 'null'
          description: Name of the class.
          examples:
          - car
          - person
        threshold:
          anyOf:
          - maximum: 1.0
            minimum: 0.0
            type: number
          - type: 'null'
          default: 0.5
          description: Threshold for the class.
          title: Threshold
      required:
      - name
      title: Classes
      type: object
    Config:
      properties:
        log_level_debug:
          anyOf:
          - type: boolean
          - type: 'null'
          default: false
          description: Whether to log debug messages or not.
          title: Log Level Debug
        log_output_path:
          anyOf:
          - type: string
          - type: 'null'
          default: logs.log
          description: The path where the logs will be saved.
          examples:
          - /tmp/eyeq-tracker-logs
          title: Log Output Path
      title: Config
      type: object
    Detector:
      properties:
        classes:
          default:
          - max_age: 6
            min_hits: 3
            name: car
            threshold: 0.5
          - max_age: 6
            min_hits: 3
            name: person
            threshold: 0.5
          description: The classes to detect.
          items:
            $ref: '#/components/schemas/Classes'
          title: Classes
          type: array
        detector_name:
          anyOf:
          - $ref: '#/components/schemas/DetectorName'
          - type: 'null'
          default: yolov4
          description: The name of the detector.
          examples:
          - rtdetr
          - yolov4
        routing_key:
          anyOf:
          - const: eyeq-detector
            type: string
          - type: 'null'
          default: eyeq-detector
          deprecated: true
          description: The routing key of the detector.
          title: Routing Key
      title: Detector
      type: object
    DetectorName:
      enum:
      - indoor-yolov4
      - rtdetr
      - yolov4
      title: DetectorName
      type: string
    HTTPValidationError:
      properties:
        detail:
          items:
            $ref: '#/components/schemas/ValidationError'
          title: Detail
          type: array
      title: HTTPValidationError
      type: object
    IndoorDetector:
      properties:
        classes:
          default:
          - max_age: 6
            min_hits: 3
            name: person
            threshold: 0.3
          description: The classes to detect.
          items:
            $ref: '#/components/schemas/Classes'
          title: Classes
          type: array
        conf_threshold:
          anyOf:
          - maximum: 1.0
            minimum: 0.0
            type: number
          - type: 'null'
          default: 0.5
          description: Confidence threshold (specific to Tracker Features)
          title: Conf Threshold
        detector_name:
          anyOf:
          - $ref: '#/components/schemas/DetectorName'
          - type: 'null'
          default: indoor-yolov4
          description: The name of the detector.
          examples:
          - rtdetr
          - yolov4
        iou_threshold:
          anyOf:
          - maximum: 1.0
            minimum: 0.0
            type: number
          - type: 'null'
          default: 0.5
          description: IoU threshold (specific to Tracker Features)
          title: Iou Threshold
        routing_key:
          anyOf:
          - const: eyeq-detector
            type: string
          - type: 'null'
          default: eyeq-detector
          deprecated: true
          description: The routing key of the detector.
          title: Routing Key
      title: IndoorDetector
      type: object
    IndoorRoi:
      properties:
        coordinates:
          description: The coordinates of the ROI.
          examples:
          - - - 0.0
              - 0.0
            - - 0.0
              - 1.0
            - - 1.0
              - 1.0
            - - 1.0
              - 0.0
          items:
            items:
              maximum: 1.0
              minimum: 0.0
              type: number
            maxItems: 2
            minItems: 2
            type: array
          title: Coordinates
          type: array
          visible: true
        extra:
          advanced: true
          anyOf:
          - type: object
          - type: 'null'
          description: Extra parameters that can be used by the ROI.
          title: Extra
        groupId:
          anyOf:
          - format: uuid
            type: string
          - type: 'null'
          description: UUID of the ROI group.
          immutable: true
          title: Groupid
        groupName:
          anyOf:
          - type: string
          - type: 'null'
          description: Name of the ROI group.
          title: Groupname
        id:
          description: The id of the ROI. Must be unique.
          examples:
          - deliver
          title: Id
          type: string
        location:
          description: Location of the ROI.
          examples:
          - kitchen
          - dining_room
          placeholder: kitchen
          title: Location
          type: string
        minimumModIou:
          advanced: true
          anyOf:
          - maximum: 1.0
            minimum: 0.0
            type: number
          - type: 'null'
          description: Minimum intersection between the person and the RoI.
          examples:
          - '0.3'
          - '0.5'
          placeholder: 0.3
          title: Minimummodiou
        type:
          description: The type of the ROI.
          enum:
          - chip_bay|chip_bay_state
          - customer|staff_customer_stats
          - floor|customer_in_line_stats
          - staff|staff_customer_stats
          - table|table_status
          - workstation|workstation_stats
          examples:
          - workstation|workstation_stats
          immutable: true
          title: Type
          type: string
        uuid:
          description: UUID for the ROI
          format: uuid
          immutable: true
          title: Uuid
          type: string
      required:
      - coordinates
      - id
      - type
      - uuid
      - location
      title: IndoorRoi
      type: object
    IndoorTracker:
      properties:
        authorName:
          anyOf:
          - format: email
            type: string
          - type: string
          description: The name of the author of the last change.
          examples:
          - <EMAIL>
          - Person Name
          title: Authorname
        camera:
          $ref: '#/components/schemas/Camera'
          description: The configuration options for the camera.
        cameraId:
          description: The id of the tracker.
          examples:
          - eyeq-tracker-fm-mcd-aus-1305-camera15
          - eyeq-tracker-fm-cfa-usa-01490-camera148
          pattern: ^eyeq-tracker-fm-[a-z]{3}(-[a-z]{2,3})?-\d{3,5}-camera\d{1,4}$
          title: Cameraid
          type: string
        config:
          $ref: '#/components/schemas/Config'
          description: The configuration options for the application.
        detector:
          $ref: '#/components/schemas/IndoorDetector'
          description: The configuration options for the detector.
        lastUpdatedTimestamp:
          description: The timestamp of the last update.
          format: date-time
          title: Lastupdatedtimestamp
          type: string
        rois:
          description: The configuration options for the ROIs.
          items:
            $ref: '#/components/schemas/IndoorRoi'
          title: Rois
          type: array
        service:
          const: Indoor
          default: Indoor
          description: The service that is running on camera (eyecue or indoor).
          title: Service
          type: string
        siteId:
          description: The id of the site
          examples:
          - fm-mcd-aus-1305
          - fm-cfa-usa-01490
          pattern: ^fm-[a-z]{3}(-[a-z]{2,3})?-\d{3,5}$
          title: Siteid
          type: string
        tracking:
          $ref: '#/components/schemas/eyecue_things_api__models__tracker__indoor_tracker__Tracking'
          description: The configuration options for the tracking.
        uuid:
          description: UUID for the tracker object
          format: uuid
          title: Uuid
          type: string
      required:
      - authorName
      - cameraId
      - siteId
      - camera
      title: IndoorTracker
      type: object
    InstallationCamera:
      properties:
        id:
          description: The camera_id of the RoIs, inform only numbers.
          examples:
          - '015'
          pattern: ^\d{1,5}$
          title: Id
          type: string
        rois:
          description: The RoIs for the camera
          items:
            anyOf:
            - $ref: '#/components/schemas/IndoorRoi'
            - $ref: '#/components/schemas/Roi'
          title: Rois
          type: array
      required:
      - id
      - rois
      title: InstallationCamera
      type: object
    InteractionPoint:
      properties:
        x:
          default: 0.5
          description: The x coordinate of the interaction point.
          maximum: 1.0
          minimum: 0.0
          title: X
          type: number
        y:
          default: 0.5
          description: The y coordinate of the interaction point.
          maximum: 1.0
          minimum: 0.0
          title: Y
          type: number
      title: InteractionPoint
      type: object
    Roi:
      properties:
        allowReenter:
          anyOf:
          - type: boolean
          - type: 'null'
          default: false
          deprecated: true
          description: Whether to allow a car to reenter the ROI (Deprecated).
          title: Allowreenter
        changeToQueueNFrames:
          anyOf:
          - type: number
          - type: 'null'
          default: 5
          description: Number of frames in a row with cars in queue to change the
            final status of the ROI to True (queue present).
          title: Changetoqueuenframes
        change_NO_queue_n_frames:
          anyOf:
          - type: number
          - type: 'null'
          default: 20
          description: Number of frames in a row with no cars in queue to change the
            final status of the ROI to False (no queue).
          title: Change No Queue N Frames
        coordinates:
          description: The coordinates of the ROI.
          examples:
          - - - 0.0
              - 0.0
            - - 0.0
              - 1.0
            - - 1.0
              - 1.0
            - - 1.0
              - 0.0
          items:
            items:
              maximum: 1.0
              minimum: 0.0
              type: number
            maxItems: 2
            minItems: 2
            type: array
          title: Coordinates
          type: array
        deviceInteraction:
          anyOf:
          - type: boolean
          - type: 'null'
          default: false
          description: Enable this feature if the Device-Driver Interaction occurs
            within the ROI instead of Human-Vehicle Interaction.
          title: Deviceinteraction
        distanceThreshold:
          anyOf:
          - maximum: 1.0
            minimum: 0.0
            type: number
          - type: 'null'
          default: 0
          description: 'Percentage used to calculate whether a car is following a
            trajectory or not. The final distance will be based on the size of the
            car, and the specified percentage, if functionality `trajectory` present. '
          title: Distancethreshold
        enableHviDfa:
          anyOf:
          - type: boolean
          - type: 'null'
          default: false
          description: By default, HVI with DFA is only enabled for cfa-usa clients.
            Use this variable to enable for other clients.
          title: Enablehvidfa
        eventThreshold:
          anyOf:
          - type: integer
          - type: 'null'
          default: 5
          description: Specify the number of frames that the car must not have been
            following atrajectory to be considered and event of interest (baulk or
            early-leave), if functionality `trajectory` present.
          title: Eventthreshold
        filters:
          anyOf:
          - items:
              $ref: '#/components/schemas/RoiConfigFilter'
            type: array
          - type: 'null'
          description: The filters that will be applied to the ROI. When an asset
            is filtered it won't generate metrics.
          title: Filters
        functionalZone:
          anyOf:
          - type: string
          - type: 'null'
          description: The name of the functional zone.
          examples:
          - order-canopy
          title: Functionalzone
        functionalities:
          description: The functionalities that the ROI will have.
          examples:
          - best_shot
          - edge_roi
          - embedding
          items:
            enum:
            - best_shot
            - edge_roi
            - embedding
            - entry_count
            - queue
            - stream_roi
            - timer
            - trajectory_roi
            type: string
          title: Functionalities
          type: array
        hviAngleThreshold:
          anyOf:
          - maximum: 180.0
            minimum: 0.0
            type: number
          - type: 'null'
          default: 30.0
          description: The threshold angle between the orthogonal vector of the vehicle
            flow and a vector composed of the center of the vehicle's bounding box
            and the center of the person's bounding box.
          title: Hvianglethreshold
        hviDfaKeepAlive:
          anyOf:
          - minimum: 1.0
            type: integer
          - type: 'null'
          default: 6
          description: The amount of (consecutive) frames that the DFA should keep
            tracking an event if no interaction (between the person and vehicle) occurs.
          title: Hvidfakeepalive
        hviDfaMinDistance:
          anyOf:
          - minimum: 0.0
            type: number
          - type: 'null'
          default: 1.5
          description: The minimum distance between a person and a vehicle to be considered
            an interaction.
          title: Hvidfamindistance
        hviDfaMinTime:
          anyOf:
          - minimum: 0.0
            type: integer
          - type: 'null'
          default: 9
          description: The minimum amount of frames that an interaction (between a
            vehicle and a person) should happen to start an event.
          title: Hvidfamintime
        hviDistVelocityPenalty:
          anyOf:
          - type: boolean
          - type: 'null'
          default: false
          description: Whether to penalize the distance between a person and a vehicle
            by the velocity of the vehicle.
          title: Hvidistvelocitypenalty
        hviFilterByAngle:
          anyOf:
          - type: boolean
          - type: 'null'
          default: true
          description: Should filter HVI by checking angle of the vehicle-person vector
            and the vehicle flow.
          title: Hvifilterbyangle
        hviFilterPersonRoi:
          anyOf:
          - type: boolean
          - type: 'null'
          default: false
          description: Should only consider HVIs where the person is in the same ROI
            of the vehicle.
          title: Hvifilterpersonroi
        hviSelectStoppedCar:
          anyOf:
          - type: boolean
          - type: 'null'
          default: false
          description: Should only consider HVIs where the vehicle is stopped.
          title: Hviselectstoppedcar
        hviSelectStoppedPerson:
          anyOf:
          - type: boolean
          - type: 'null'
          default: false
          description: Should only consider HVIs where the person is stopped.
          title: Hviselectstoppedperson
        hviThresholdDistance:
          anyOf:
          - type: number
          - type: 'null'
          default: 1.5
          deprecated: true
          description: Threshold distance between the centroids of a cars and a persons
            bounding boxes is used to determine whether an interaction between a human
            and a vehicle is taking place. This is only considered when the overhead
            option is enabled. (This will be deprecated).
          title: Hvithresholddistance
        hviUseFlowPoints:
          anyOf:
          - type: boolean
          - type: 'null'
          default: true
          description: Should use better points (instead of centroids) to measure
            distance between vehicle and person (this feature require configuration
            of hvi_filter_by_angle).
          title: Hviuseflowpoints
        hviVehicleFlow:
          anyOf:
          - maximum: 12.0
            minimum: 1.0
            type: integer
          - type: 'null'
          default: 6
          description: The direction of the flow in the camera using clock pointer
            direction notation.
          title: Hvivehicleflow
        hviWidthPenalty:
          anyOf:
          - type: boolean
          - type: 'null'
          default: true
          description: For hvi distance estimation, if a car is too close to the camera
            then the width of the car is penalized.
          title: Hviwidthpenalty
        id:
          description: The id of the ROI. Must be unique.
          examples:
          - deliver
          title: Id
          type: string
        interactionPoint:
          anyOf:
          - $ref: '#/components/schemas/InteractionPoint'
          - type: 'null'
          description: The interaction point of the centre of the asset that will
            be used to check if an asset is inside this ROI.
        interactionRoi:
          anyOf:
          - type: boolean
          - type: 'null'
          default: false
          description: Whether the ROI is an interaction ROI. I.e If the ROI is an
            interaction ROI it will check if there's human-vehicle interaction inside
            this ROI.
          title: Interactionroi
        laneNumber:
          anyOf:
          - type: integer
          - type: 'null'
          default: 1
          description: The lane number of this ROI. Used to report with the events
            generated.
          title: Lanenumber
        linear:
          anyOf:
          - type: boolean
          - type: 'null'
          default: true
          description: Whether this is a linear drive thru. Eyecue don't allow assets
            to just appear in the middle of the reid node, meaning they must have
            come from a previous node. When this values is set to False Eyecue allow
            an asset to start from this node.
          title: Linear
        lookupDepth:
          anyOf:
          - minimum: -1.0
            type: integer
          - type: 'null'
          default: 2
          description: Depth to look for a candidate in the pool of candidates. When
            set to -1 every candidate will be selected.
          title: Lookupdepth
        order:
          anyOf:
          - type: string
          - type: 'null'
          description: The order of the ROI. Used in best shot to represent the order
            of the REID Graph.
          examples:
          - A.B.C
          - A.B
          title: Order
        overhead:
          anyOf:
          - type: boolean
          - type: 'null'
          default: false
          deprecated: true
          description: Whether this camera is an overhead camera. This changes how
            the human-vehicle detection is calculated. (This will be deprecated).
          title: Overhead
        queueLevel:
          anyOf:
          - exclusiveMinimum: 0.0
            type: integer
          - type: 'null'
          description: The level of the queue, positive number.
          examples:
          - 1
          - 2
          - 3
          - 4
          - 5
          title: Queuelevel
        relSpeedThreshold:
          anyOf:
          - type: number
          - type: 'null'
          default: 0.02
          description: Maximum relative speed above which the cars are not considered
            to be in queue (there is no queue if the cars are moving too fast). Relative
            speed is calculated as the ratio of the car's movement in pixels in the
            last frame to the car's size in pixels.
          title: Relspeedthreshold
        rtype:
          anyOf:
          - enum:
            - fifo
            - tbfifo
            - lofi
            - visual
            - none
            type: string
          - type: 'null'
          description: The re-acquirement algorithm to be used.
          examples:
          - fifo
          - visual
          title: Rtype
        timeBetweenEventsThreshold:
          anyOf:
          - type: number
          - type: 'null'
          default: 0
          deprecated: true
          description: The time in seconds that must pass between events for the ROI
            to create a new event (Deprecated).
          title: Timebetweeneventsthreshold
        type:
          description: The type of the ROI.
          examples:
          - DWELL
          - DELIVER
          title: Type
          type: string
        uuid:
          description: UUID for the ROI
          format: uuid
          title: Uuid
          type: string
        visualThreshold:
          anyOf:
          - maximum: 1.0
            minimum: 0.0
            type: number
          - type: 'null'
          default: 1
          description: Threshold to consider a candidate a match. The lower it gets
            more confident it is.
          title: Visualthreshold
      required:
      - coordinates
      - id
      - type
      title: Roi
      type: object
    RoiConfigFilter:
      properties:
        max_area:
          anyOf:
          - type: integer
          - type: 'null'
          default: 2
          description: The maximum area of the car normalized in pixels.
          title: Max Area
        max_height:
          anyOf:
          - type: integer
          - type: 'null'
          default: 2
          description: The maximum height of the car normalized in pixels.
          title: Max Height
        max_width:
          anyOf:
          - type: integer
          - type: 'null'
          default: 2
          description: The maximum width of the car normalized in pixels.
          title: Max Width
        min_age:
          anyOf:
          - type: integer
          - type: 'null'
          default: 0
          description: The minimum age of the car in seconds.
          title: Min Age
        min_area:
          anyOf:
          - type: integer
          - type: 'null'
          default: -2
          description: The minimum area of the car normalized in pixels.
          title: Min Area
        min_height:
          anyOf:
          - type: integer
          - type: 'null'
          default: -2
          description: The minimum height of the car normalized in pixels.
          title: Min Height
        min_width:
          anyOf:
          - type: integer
          - type: 'null'
          default: -2
          description: The minimum width of the car normalized in pixels.
          title: Min Width
        type:
          anyOf:
          - enum:
            - ROIAgeFilter
            - ROIANDFilter
            - ROIORFilter
            type: string
          - type: 'null'
          description: The type of the filter. Value that register what the type of
            the filter.
          title: Type
      required:
      - type
      title: RoiConfigFilter
      type: object
    Tracker:
      properties:
        authorName:
          anyOf:
          - format: email
            type: string
          - type: string
          description: The name of the author of the last change.
          examples:
          - <EMAIL>
          - Person Name
          title: Authorname
        camera:
          $ref: '#/components/schemas/Camera'
          description: The configuration options for the camera.
        cameraId:
          description: The id of the tracker.
          examples:
          - eyeq-tracker-fm-mcd-aus-1305-camera15
          - eyeq-tracker-fm-cfa-usa-01490-camera148
          pattern: ^eyeq-tracker-fm-[a-z]{3}(-[a-z]{2,3})?-\d{3,5}-camera\d{1,4}$
          title: Cameraid
          type: string
        config:
          anyOf:
          - $ref: '#/components/schemas/Config'
          - type: 'null'
          description: The configuration options for the application.
        detector:
          $ref: '#/components/schemas/Detector'
          description: The configuration options for the detector.
        lastUpdatedTimestamp:
          description: The timestamp of the last update.
          format: date-time
          title: Lastupdatedtimestamp
          type: string
        rois:
          description: The configuration options for the ROIs.
          items:
            $ref: '#/components/schemas/Roi'
          title: Rois
          type: array
        service:
          const: Drive-Thru
          default: Drive-Thru
          description: The service that is running on camera (eyecue or indoor).
          title: Service
          type: string
        siteId:
          description: The id of the site
          examples:
          - fm-mcd-aus-1305
          - fm-cfa-usa-01490
          pattern: ^fm-[a-z]{3}(-[a-z]{2,3})?-\d{3,5}$
          title: Siteid
          type: string
        tracking:
          anyOf:
          - $ref: '#/components/schemas/eyecue_things_api__models__tracker__tracker__Tracking'
          - type: 'null'
          description: The configuration options for the tracking.
        uuid:
          description: UUID for the tracker object
          format: uuid
          title: Uuid
          type: string
      required:
      - authorName
      - cameraId
      - siteId
      - camera
      title: Tracker
      type: object
    UseCase:
      properties:
        category:
          description: The category of the use case.
          examples:
          - Front Counter
          title: Category
          type: string
        description:
          description: The description of the use case.
          title: Description
          type: string
        edge_support:
          default: true
          description: Whether the model can be installed on the         edge or only
            on the cloud.
          title: Edge Support
          type: boolean
        image_url:
          anyOf:
          - format: uri
            maxLength: 2083
            minLength: 1
            type: string
          - type: 'null'
          description: The URL of the use case image.
          title: Image Url
        last_updated_timestamp:
          description: The timestamp of the last update.
          format: date-time
          title: Last Updated Timestamp
          type: string
        name:
          anyOf:
          - type: string
          - type: 'null'
          description: The user-friendly name of the use case.
          examples:
          - Idle Time
          title: Name
        schema_name:
          description: The schema name for the use case.
          enum:
          - chip_bay_state
          - customer_in_line_stats
          - workstation_stats
          - staff_customer_stats
          - table_status
          examples:
          - workstation_stats
          title: Schema Name
          type: string
        status:
          description: The status of the use case.
          enum:
          - Available
          - Coming Soon
          - Disabled
          examples:
          - Available
          title: Status
          type: string
        tags:
          anyOf:
          - items:
              type: string
            type: array
          - type: 'null'
          description: The tags that represent/categorize the use case.
          title: Tags
        thumbnail_url:
          description: The URL of the thumbnail of use case image.
          format: uri
          maxLength: 2083
          minLength: 1
          title: Thumbnail Url
          type: string
        use_case_id:
          description: UUID for the use case
          format: uuid
          title: Use Case Id
          type: string
        version:
          default: '1.0'
          description: The version of the use case.
          examples:
          - '1.3'
          title: Version
          type: string
      required:
      - use_case_id
      - name
      - category
      - description
      - status
      - tags
      - image_url
      - thumbnail_url
      - schema_name
      title: UseCase
      type: object
    UseCaseInstallation:
      properties:
        author_name:
          description: The author's email used to create/modify the installation.
          examples:
          - <EMAIL>
          format: email
          title: Author Name
          type: string
        cameras:
          description: The cameras and rois that are linked to the installation.
          items:
            $ref: '#/components/schemas/CameraRoiInfo'
          title: Cameras
          type: array
        installation_id:
          description: The use case and installation UUIDs combined.
          examples:
          - usecase#<uuid>|installation#<uuid>
          title: Installation Id
          type: string
        last_updated_timestamp:
          description: The timestamp of the last update.
          format: date-time
          title: Last Updated Timestamp
          type: string
        site_id:
          description: The site_id of the installation.
          pattern: ^fm-[a-z]{3}(-[a-z]{2,3})?-\d{3,5}$
          title: Site Id
          type: string
      required:
      - site_id
      - installation_id
      - cameras
      - author_name
      title: UseCaseInstallation
      type: object
    UseCaseInstallationProvision:
      properties:
        author_name:
          description: The author's email used to create/modify the installation.
          examples:
          - <EMAIL>
          format: email
          title: Author Name
          type: string
        cameras:
          description: List of InstallationCamera objects,        containing camera_ids
            and RoI objects.
          items:
            $ref: '#/components/schemas/InstallationCamera'
          title: Cameras
          type: array
        site_id:
          description: The site_id of the installation.
          pattern: ^fm-[a-z]{3}(-[a-z]{2,3})?-\d{3,5}$
          title: Site Id
          type: string
        use_case_id:
          description: UUID for the ROI
          format: uuid
          title: Use Case Id
          type: string
      required:
      - site_id
      - use_case_id
      - cameras
      - author_name
      title: UseCaseInstallationProvision
      type: object
    ValidationError:
      properties:
        loc:
          items:
            anyOf:
            - type: string
            - type: integer
          title: Location
          type: array
        type:
          title: Error Type
          type: string
      required:
      - loc
      - msg
      - type
      title: ValidationError
      type: object
    eyecue_things_api__models__tracker__indoor_tracker__Tracking:
      properties:
        budget:
          anyOf:
          - minimum: 0.0
            type: integer
          - type: 'null'
          default: 100
          description: Budget for the nearest neighbors.
          title: Budget
        conf_threshold:
          anyOf:
          - maximum: 1.0
            minimum: 0.0
            type: number
          - type: 'null'
          default: 0.3
          description: Threshold for the confidence.
          title: Conf Threshold
        enabled:
          anyOf:
          - type: boolean
          - type: 'null'
          default: true
          description: Should enable tracking.
          title: Enabled
        iou_threshold:
          anyOf:
          - maximum: 1.0
            minimum: 0.0
            type: number
          - type: 'null'
          default: 0.1
          description: Threshold for the IOU.
          title: Iou Threshold
        iou_threshold_for_unmatched_detections:
          anyOf:
          - maximum: 1.0
            minimum: 0.0
            type: number
          - type: 'null'
          default: 0.01
          description: Threshold for the IOU for unmatched detections.
          title: Iou Threshold For Unmatched Detections
        max_age:
          anyOf:
          - minimum: 0.0
            type: integer
          - type: 'null'
          default: 70
          description: Maximum age of the tracker before it gets deleted if it is
            not updated.
          title: Max Age
        max_distance:
          anyOf:
          - maximum: 1.0
            minimum: 0.0
            type: number
          - type: 'null'
          default: 0.2
          description: Maximum distance for the tracker.
          title: Max Distance
        max_iou_distance:
          anyOf:
          - maximum: 1.0
            minimum: 0.0
            type: number
          - type: 'null'
          default: 0.7
          description: Maximum IOU distance.
          title: Max Iou Distance
        model:
          default: deep_sort
          description: The model to use for the tracker.
          title: Model
          type: string
        n_init:
          anyOf:
          - type: integer
          - type: 'null'
          default: 3
          description: Minimum number of hits before the tracker is considered valid.
          title: N Init
        reid_enabled:
          anyOf:
          - type: boolean
          - type: 'null'
          default: true
          description: Should perform reid.
          title: Reid Enabled
        version:
          anyOf:
          - type: string
          - type: 'null'
          default: qsr-1.1
          description: The version of the model to use for the tracker.
          title: Version
      title: Tracking
      type: object
    eyecue_things_api__models__tracker__tracker__Tracking:
      properties:
        max_age:
          anyOf:
          - minimum: 1.0
            type: integer
          - type: 'null'
          default: 6
          title: Max Age
        min_hits:
          anyOf:
          - minimum: 1.0
            type: integer
          - type: 'null'
          default: 3
          title: Min Hits
        name:
          anyOf:
          - type: string
          - type: 'null'
          deprecated: true
          description: The name of the tracker.
          examples:
          - tracker-name
          title: Name
        threshold:
          anyOf:
          - maximum: 1.0
            minimum: 0.0
            type: number
          - type: 'null'
          default: 0.5
          title: Threshold
      title: Tracking
      type: object
  securitySchemes:
    x-api-key:
      in: header
      name: x-api-key
      type: apiKey
info:
  title: Eyecue Vision API
  version: 1.0.0
openapi: 3.1.0
paths:
  /installations:
    get:
      operationId: getAllUseCaseInstallations
      responses:
        '200':
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/UseCaseInstallation'
                title: Return
                type: array
          description: Success
        '403':
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/UseCaseInstallation'
                title: Return
                type: array
          description: Forbidden
        '404':
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/UseCaseInstallation'
                title: Return
                type: array
          description: Not Found
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
        '500':
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/UseCaseInstallation'
                title: Return
                type: array
          description: Server Error
      summary: Get list of all available use cases installations.
      tags:
      - Use Cases Installations
    post:
      operationId: createUseCaseInstallation
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UseCaseInstallation'
              description: Use case installation payload.
        description: Use case installation payload.
        required: true
      responses:
        '200':
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/UseCaseInstallation'
                title: Return
                type: array
          description: Success
        '403':
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/UseCaseInstallation'
                title: Return
                type: array
          description: Forbidden
        '404':
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/UseCaseInstallation'
                title: Return
                type: array
          description: Not Found
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
        '500':
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/UseCaseInstallation'
                title: Return
                type: array
          description: Server Error
      summary: Create a new use case installation
      tags:
      - Use Cases Installations
  /installations/provision:
    post:
      operationId: provisionUseCaseInstallation
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UseCaseInstallationProvision'
              description: Use case installation payload.
        description: Use case installation payload.
        required: true
      responses:
        '200':
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/UseCaseInstallation'
                title: Return
                type: array
          description: Success
        '403':
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/UseCaseInstallation'
                title: Return
                type: array
          description: Forbidden
        '404':
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/UseCaseInstallation'
                title: Return
                type: array
          description: Not Found
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
        '500':
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/UseCaseInstallation'
                title: Return
                type: array
          description: Server Error
      summary: Provision a new use case installation and create all of its dependencies.
      tags:
      - Use Cases Installations
  /installations/sites/{site_id}:
    get:
      operationId: getUseCaseInstallations
      parameters:
      - description: Filter use case installations by                                  the
          site id of the store.
        in: path
        name: site_id
        required: true
        schema:
          description: Filter use case installations by                                  the
            site id of the store.
          title: Site Id
          type: string
      - description: (Optional) Filter use case installation by                the
          use_case_id (UUID).
        in: query
        name: use_case_id
        required: false
        schema:
          anyOf:
          - type: string
          - type: 'null'
          description: (Optional) Filter use case installation by                the
            use_case_id (UUID).
          examples:
          - d5fbdf86-4dd0-4bf8-9932-be4e1aac5f44
          title: Use Case Id
      responses:
        '200':
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/UseCaseInstallation'
                title: Return
                type: array
          description: Success
        '403':
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/UseCaseInstallation'
                title: Return
                type: array
          description: Forbidden
        '404':
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/UseCaseInstallation'
                title: Return
                type: array
          description: Not Found
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
        '500':
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/UseCaseInstallation'
                title: Return
                type: array
          description: Server Error
      summary: Get list of all available use cases installations         filtered
        by site id and use case id (optional).
      tags:
      - Use Cases Installations
  /installations/{installation_id}:
    put:
      operationId: updateUseCaseInstallation
      parameters:
      - description: The use case installation id.
        in: path
        name: installation_id
        required: true
        schema:
          description: The use case installation id.
          title: Installation Id
          type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UseCaseInstallation'
              description: Use case installation payload.
        description: Use case installation payload.
        required: true
      responses:
        '200':
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/UseCaseInstallation'
                title: Return
                type: array
          description: Success
        '403':
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/UseCaseInstallation'
                title: Return
                type: array
          description: Forbidden
        '404':
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/UseCaseInstallation'
                title: Return
                type: array
          description: Not Found
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
        '500':
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/UseCaseInstallation'
                title: Return
                type: array
          description: Server Error
      summary: Update an existing use case installation
      tags:
      - Use Cases Installations
  /installations/{installation_id}/sites/{site_id}:
    delete:
      operationId: deleteUseCaseInstallation
      parameters:
      - description: The use case installation id.
        in: path
        name: installation_id
        required: true
        schema:
          description: The use case installation id.
          title: Installation Id
          type: string
      - description: The site id of the store.
        in: path
        name: site_id
        required: true
        schema:
          description: The site id of the store.
          title: Site Id
          type: string
      responses:
        '200':
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/UseCaseInstallation'
                title: Return
                type: array
          description: Success
        '403':
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/UseCaseInstallation'
                title: Return
                type: array
          description: Forbidden
        '404':
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/UseCaseInstallation'
                title: Return
                type: array
          description: Not Found
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
        '500':
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/UseCaseInstallation'
                title: Return
                type: array
          description: Server Error
      summary: Delete an existing use case installation
      tags:
      - Use Cases Installations
  /schemas/installations:
    get:
      operationId: getUseCaseInstallationSchema
      responses:
        '200':
          content:
            application/json:
              schema:
                title: Return
                type: object
          description: Success
        '403':
          content:
            application/json:
              schema:
                title: Return
                type: object
          description: Forbidden
        '404':
          content:
            application/json:
              schema:
                title: Return
                type: object
          description: Not Found
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
        '500':
          content:
            application/json:
              schema:
                title: Return
                type: object
          description: Server Error
      summary: Get JSON Schema for the use cases installations.
      tags:
      - Schemas
  /schemas/rois/{roi_type}:
    get:
      operationId: getRoISchema
      parameters:
      - description: 'The site id of the store. Ex.: Indoor, Drive-Thru.'
        in: path
        name: roi_type
        required: true
        schema:
          description: 'The site id of the store. Ex.: Indoor, Drive-Thru.'
          title: Roi Type
          type: string
      responses:
        '200':
          content:
            application/json:
              schema:
                title: Return
                type: object
          description: Success
        '403':
          content:
            application/json:
              schema:
                title: Return
                type: object
          description: Forbidden
        '404':
          content:
            application/json:
              schema:
                title: Return
                type: object
          description: Not Found
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
        '500':
          content:
            application/json:
              schema:
                title: Return
                type: object
          description: Server Error
      summary: Get JSON Schema for RoIs
      tags:
      - Schemas
  /schemas/use-case-rois/{use_case_id}:
    get:
      operationId: getUseCaseRoiSchema
      parameters:
      - description: The RoI JSON Schema for the use case.
        in: path
        name: use_case_id
        required: true
        schema:
          description: The RoI JSON Schema for the use case.
          title: Use Case Id
          type: string
      responses:
        '200':
          content:
            application/json:
              schema:
                title: Return
                type: object
          description: Success
        '403':
          content:
            application/json:
              schema:
                title: Return
                type: object
          description: Forbidden
        '404':
          content:
            application/json:
              schema:
                title: Return
                type: object
          description: Not Found
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
        '500':
          content:
            application/json:
              schema:
                title: Return
                type: object
          description: Server Error
      summary: Get RoI JSON Schema for the requested use case
      tags:
      - Schemas
  /schemas/use-cases:
    get:
      operationId: getUseCaseSchema
      responses:
        '200':
          content:
            application/json:
              schema:
                title: Return
                type: object
          description: Success
        '403':
          content:
            application/json:
              schema:
                title: Return
                type: object
          description: Forbidden
        '404':
          content:
            application/json:
              schema:
                title: Return
                type: object
          description: Not Found
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
        '500':
          content:
            application/json:
              schema:
                title: Return
                type: object
          description: Server Error
      summary: Get JSON Schema for a use case.
      tags:
      - Schemas
  /sites/{site_id}/cameras:
    post:
      operationId: createCamera
      parameters:
      - description: The site id of the store
        in: path
        name: site_id
        required: true
        schema:
          description: The site id of the store
          title: Site Id
          type: string
      requestBody:
        content:
          application/json:
            schema:
              description: Indoor or Drive-Thru tracker
              discriminator:
                mapping:
                  Drive-Thru: '#/components/schemas/Tracker'
                  Indoor: '#/components/schemas/IndoorTracker'
                propertyName: service
              oneOf:
              - $ref: '#/components/schemas/IndoorTracker'
              - $ref: '#/components/schemas/Tracker'
              title: Tracker
        description: Indoor or Drive-Thru tracker
        required: true
      responses:
        '200':
          content:
            application/json:
              schema:
                anyOf:
                - $ref: '#/components/schemas/IndoorTracker'
                - $ref: '#/components/schemas/Tracker'
                title: Return
          description: Success
        '403':
          content:
            application/json:
              schema:
                anyOf:
                - $ref: '#/components/schemas/IndoorTracker'
                - $ref: '#/components/schemas/Tracker'
                title: Return
          description: Forbidden
        '404':
          content:
            application/json:
              schema:
                anyOf:
                - $ref: '#/components/schemas/IndoorTracker'
                - $ref: '#/components/schemas/Tracker'
                title: Return
          description: Not Found
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
        '500':
          content:
            application/json:
              schema:
                anyOf:
                - $ref: '#/components/schemas/IndoorTracker'
                - $ref: '#/components/schemas/Tracker'
                title: Return
          description: Server Error
      summary: Create camera with site and camera ID
      tags:
      - Cameras
  /sites/{site_id}/cameras/{camera_id}:
    delete:
      operationId: deleteCamera
      parameters:
      - description: The site id of the store
        in: path
        name: site_id
        required: true
        schema:
          description: The site id of the store
          title: Site Id
          type: string
      - description: 'The camera id as numbers (example: 019)'
        in: path
        name: camera_id
        required: true
        schema:
          description: 'The camera id as numbers (example: 019)'
          title: Camera Id
          type: string
      responses:
        '200':
          content:
            application/json:
              schema:
                anyOf:
                - $ref: '#/components/schemas/IndoorTracker'
                - $ref: '#/components/schemas/Tracker'
                title: Return
          description: Success
        '403':
          content:
            application/json:
              schema:
                anyOf:
                - $ref: '#/components/schemas/IndoorTracker'
                - $ref: '#/components/schemas/Tracker'
                title: Return
          description: Forbidden
        '404':
          content:
            application/json:
              schema:
                anyOf:
                - $ref: '#/components/schemas/IndoorTracker'
                - $ref: '#/components/schemas/Tracker'
                title: Return
          description: Not Found
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
        '500':
          content:
            application/json:
              schema:
                anyOf:
                - $ref: '#/components/schemas/IndoorTracker'
                - $ref: '#/components/schemas/Tracker'
                title: Return
          description: Server Error
      summary: Delete camera tracker
      tags:
      - Cameras
    get:
      operationId: getCamera
      parameters:
      - description: The site id of the store
        in: path
        name: site_id
        required: true
        schema:
          description: The site id of the store
          title: Site Id
          type: string
      - description: 'The camera id as numbers (example: 019)'
        in: path
        name: camera_id
        required: true
        schema:
          description: 'The camera id as numbers (example: 019)'
          title: Camera Id
          type: string
      responses:
        '200':
          content:
            application/json:
              schema:
                anyOf:
                - $ref: '#/components/schemas/IndoorTracker'
                - $ref: '#/components/schemas/Tracker'
                title: Return
          description: Success
        '403':
          content:
            application/json:
              schema:
                anyOf:
                - $ref: '#/components/schemas/IndoorTracker'
                - $ref: '#/components/schemas/Tracker'
                title: Return
          description: Forbidden
        '404':
          content:
            application/json:
              schema:
                anyOf:
                - $ref: '#/components/schemas/IndoorTracker'
                - $ref: '#/components/schemas/Tracker'
                title: Return
          description: Not Found
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
        '500':
          content:
            application/json:
              schema:
                anyOf:
                - $ref: '#/components/schemas/IndoorTracker'
                - $ref: '#/components/schemas/Tracker'
                title: Return
          description: Server Error
      summary: Get camera by site and camera ID
      tags:
      - Cameras
    put:
      operationId: updateCamera
      parameters:
      - description: The site id of the store
        in: path
        name: site_id
        required: true
        schema:
          description: The site id of the store
          title: Site Id
          type: string
      - description: 'The camera id as numbers (example: 019)'
        in: path
        name: camera_id
        required: true
        schema:
          description: 'The camera id as numbers (example: 019)'
          title: Camera Id
          type: string
      requestBody:
        content:
          application/json:
            schema:
              description: Indoor or Drive-Thru tracker
              discriminator:
                mapping:
                  Drive-Thru: '#/components/schemas/Tracker'
                  Indoor: '#/components/schemas/IndoorTracker'
                propertyName: service
              oneOf:
              - $ref: '#/components/schemas/IndoorTracker'
              - $ref: '#/components/schemas/Tracker'
              title: Tracker
        description: Indoor or Drive-Thru tracker
        required: true
      responses:
        '200':
          content:
            application/json:
              schema:
                anyOf:
                - $ref: '#/components/schemas/IndoorTracker'
                - $ref: '#/components/schemas/Tracker'
                title: Return
          description: Success
        '403':
          content:
            application/json:
              schema:
                anyOf:
                - $ref: '#/components/schemas/IndoorTracker'
                - $ref: '#/components/schemas/Tracker'
                title: Return
          description: Forbidden
        '404':
          content:
            application/json:
              schema:
                anyOf:
                - $ref: '#/components/schemas/IndoorTracker'
                - $ref: '#/components/schemas/Tracker'
                title: Return
          description: Not Found
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
        '500':
          content:
            application/json:
              schema:
                anyOf:
                - $ref: '#/components/schemas/IndoorTracker'
                - $ref: '#/components/schemas/Tracker'
                title: Return
          description: Server Error
      summary: Update camera with site and camera ID
      tags:
      - Cameras
  /sites/{site_id}/cameras/{camera_id}/rois:
    post:
      operationId: createRoI
      parameters:
      - description: The site id of the store
        in: path
        name: site_id
        required: true
        schema:
          description: The site id of the store
          title: Site Id
          type: string
      - description: 'The camera id as numbers (example: 019)'
        in: path
        name: camera_id
        required: true
        schema:
          description: 'The camera id as numbers (example: 019)'
          title: Camera Id
          type: string
      requestBody:
        content:
          application/json:
            schema:
              anyOf:
              - items:
                  anyOf:
                  - $ref: '#/components/schemas/IndoorRoi'
                  - $ref: '#/components/schemas/Roi'
                type: array
              - $ref: '#/components/schemas/IndoorRoi'
              - $ref: '#/components/schemas/Roi'
              description: Tracker RoIs payload
              title: Rois
        description: Tracker RoIs payload
        required: true
      responses:
        '200':
          content:
            application/json:
              schema:
                anyOf:
                - $ref: '#/components/schemas/IndoorRoi'
                - $ref: '#/components/schemas/Roi'
                title: Return
          description: Success
        '403':
          content:
            application/json:
              schema:
                anyOf:
                - $ref: '#/components/schemas/IndoorRoi'
                - $ref: '#/components/schemas/Roi'
                title: Return
          description: Forbidden
        '404':
          content:
            application/json:
              schema:
                anyOf:
                - $ref: '#/components/schemas/IndoorRoi'
                - $ref: '#/components/schemas/Roi'
                title: Return
          description: Not Found
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
        '500':
          content:
            application/json:
              schema:
                anyOf:
                - $ref: '#/components/schemas/IndoorRoi'
                - $ref: '#/components/schemas/Roi'
                title: Return
          description: Server Error
      summary: Create RoIs for tracker
      tags:
      - Rois
  /sites/{site_id}/cameras/{camera_id}/rois/{roi_id}:
    delete:
      operationId: deleteRoI
      parameters:
      - description: The site id of the store
        in: path
        name: site_id
        required: true
        schema:
          description: The site id of the store
          title: Site Id
          type: string
      - description: 'The camera id as numbers (example: 019)'
        in: path
        name: camera_id
        required: true
        schema:
          description: 'The camera id as numbers (example: 019)'
          title: Camera Id
          type: string
      - description: The UUID of th RoI
        in: path
        name: roi_id
        required: true
        schema:
          description: The UUID of th RoI
          title: Roi Id
          type: string
      responses:
        '200':
          content:
            application/json:
              schema:
                anyOf:
                - $ref: '#/components/schemas/IndoorRoi'
                - $ref: '#/components/schemas/Roi'
                title: Return
          description: Success
        '403':
          content:
            application/json:
              schema:
                anyOf:
                - $ref: '#/components/schemas/IndoorRoi'
                - $ref: '#/components/schemas/Roi'
                title: Return
          description: Forbidden
        '404':
          content:
            application/json:
              schema:
                anyOf:
                - $ref: '#/components/schemas/IndoorRoi'
                - $ref: '#/components/schemas/Roi'
                title: Return
          description: Not Found
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
        '500':
          content:
            application/json:
              schema:
                anyOf:
                - $ref: '#/components/schemas/IndoorRoi'
                - $ref: '#/components/schemas/Roi'
                title: Return
          description: Server Error
      summary: Delete RoI for tracker
      tags:
      - Rois
    get:
      operationId: getRoi
      parameters:
      - description: The site id of the store
        in: path
        name: site_id
        required: true
        schema:
          description: The site id of the store
          title: Site Id
          type: string
      - description: 'The camera id as numbers (example: 019)'
        in: path
        name: camera_id
        required: true
        schema:
          description: 'The camera id as numbers (example: 019)'
          title: Camera Id
          type: string
      - description: The UUID of th RoI
        in: path
        name: roi_id
        required: true
        schema:
          description: The UUID of th RoI
          title: Roi Id
          type: string
      responses:
        '200':
          content:
            application/json:
              schema:
                anyOf:
                - $ref: '#/components/schemas/IndoorRoi'
                - $ref: '#/components/schemas/Roi'
                title: Return
          description: Success
        '403':
          content:
            application/json:
              schema:
                anyOf:
                - $ref: '#/components/schemas/IndoorRoi'
                - $ref: '#/components/schemas/Roi'
                title: Return
          description: Forbidden
        '404':
          content:
            application/json:
              schema:
                anyOf:
                - $ref: '#/components/schemas/IndoorRoi'
                - $ref: '#/components/schemas/Roi'
                title: Return
          description: Not Found
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
        '500':
          content:
            application/json:
              schema:
                anyOf:
                - $ref: '#/components/schemas/IndoorRoi'
                - $ref: '#/components/schemas/Roi'
                title: Return
          description: Server Error
      summary: Get RoI
      tags:
      - Rois
    put:
      operationId: updateRoI
      parameters:
      - description: The site id of the store
        in: path
        name: site_id
        required: true
        schema:
          description: The site id of the store
          title: Site Id
          type: string
      - description: 'The camera id as numbers (example: 019)'
        in: path
        name: camera_id
        required: true
        schema:
          description: 'The camera id as numbers (example: 019)'
          title: Camera Id
          type: string
      - description: The UUID of th RoI
        in: path
        name: roi_id
        required: true
        schema:
          description: The UUID of th RoI
          title: Roi Id
          type: string
      requestBody:
        content:
          application/json:
            schema:
              anyOf:
              - $ref: '#/components/schemas/IndoorRoi'
              - $ref: '#/components/schemas/Roi'
              description: Tracker RoI payload
              title: Roi
        description: Tracker RoI payload
        required: true
      responses:
        '200':
          content:
            application/json:
              schema:
                anyOf:
                - $ref: '#/components/schemas/IndoorRoi'
                - $ref: '#/components/schemas/Roi'
                title: Return
          description: Success
        '403':
          content:
            application/json:
              schema:
                anyOf:
                - $ref: '#/components/schemas/IndoorRoi'
                - $ref: '#/components/schemas/Roi'
                title: Return
          description: Forbidden
        '404':
          content:
            application/json:
              schema:
                anyOf:
                - $ref: '#/components/schemas/IndoorRoi'
                - $ref: '#/components/schemas/Roi'
                title: Return
          description: Not Found
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
        '500':
          content:
            application/json:
              schema:
                anyOf:
                - $ref: '#/components/schemas/IndoorRoi'
                - $ref: '#/components/schemas/Roi'
                title: Return
          description: Server Error
      summary: Update RoI for tracker
      tags:
      - Rois
  /use-cases:
    get:
      operationId: getUseCases
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UseCase'
          description: Success
        '403':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UseCase'
          description: Forbidden
        '404':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UseCase'
          description: Not Found
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
        '500':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UseCase'
          description: Server Error
      summary: Get list of all available use cases
      tags:
      - Use Cases
    post:
      operationId: createUseCase
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UseCase'
              description: Use Case payload.
        description: Use Case payload.
        required: true
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UseCase'
          description: Success
        '403':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UseCase'
          description: Forbidden
        '404':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UseCase'
          description: Not Found
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
        '500':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UseCase'
          description: Server Error
      summary: Create a new use case
      tags:
      - Use Cases
  /use-cases/{use_case_id}:
    delete:
      operationId: deleteUseCase
      parameters:
      - description: The use case id.
        in: path
        name: use_case_id
        required: true
        schema:
          description: The use case id.
          title: Use Case Id
          type: string
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UseCase'
          description: Success
        '403':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UseCase'
          description: Forbidden
        '404':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UseCase'
          description: Not Found
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
        '500':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UseCase'
          description: Server Error
      summary: Delete an existing use case
      tags:
      - Use Cases
    put:
      operationId: updateUseCase
      parameters:
      - description: The use case id.
        in: path
        name: use_case_id
        required: true
        schema:
          description: The use case id.
          title: Use Case Id
          type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UseCase'
              description: Use Case payload.
        description: Use Case payload.
        required: true
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UseCase'
          description: Success
        '403':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UseCase'
          description: Forbidden
        '404':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UseCase'
          description: Not Found
        '422':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
          description: Validation Error
        '500':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UseCase'
          description: Server Error
      summary: Update an existing use case
      tags:
      - Use Cases
security:
- x-api-key: []
servers:
- description: QA Environment
  url: https://ipqtw26rgg.execute-api.ap-southeast-2.amazonaws.com/prod/api/v1/
- description: Dev-nzl Environment
  url: https://hah78zjq94.execute-api.ap-southeast-2.amazonaws.com/dev/api/v1/
