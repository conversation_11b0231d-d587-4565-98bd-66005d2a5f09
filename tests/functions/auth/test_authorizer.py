from unittest.mock import patch, Mock

from functions.auth.handler import lambda_handler, _build_allowed_resources
from functions.auth.src.jwt import JWTClaims, AppMetadata, IQResources


ARN_BASE = "arn:aws:execute-api:ap-southeast-2:123456789012:apiId"
METHOD_ARN = f"{ARN_BASE}/dev/GET/some/path"


def build_claims(org_sites: dict[str, list[str]], sub: str = "test-user") -> JWTClaims:
    app_meta = AppMetadata(
        allowed_sites=[],
        iq_data={},
        iq_resources=IQResources(roles=[], sites=org_sites),
        organization=next(iter(org_sites.keys()), ""),
    )
    # Build claims data with all required fields and proper aliases
    claims_data = {
        # Standard JWT fields
        "iss": "https://test.auth0.com/",
        "sub": sub,
        "aud": ["https://api.test.com", "https://test.auth0.com/userinfo"],
        "iat": 1640995200,  # 2022-01-01 00:00:00 UTC
        "exp": 1640998800,  # 2022-01-01 01:00:00 UTC
        "scope": "openid profile email",
        "azp": "test-client-id",
        "permissions": ["read:data", "write:data"],

        # Custom fields with aliases
        "https://fingermark.au.auth0.comemail": "<EMAIL>",
        "https://www.fingermark.tech/roles": [],
        "https://www.fingermark.tech/databoard/app_metadata": app_meta.model_dump(),
    }
    return JWTClaims(**claims_data)


def test_build_allowed_resources_wildcard_all_sites() -> None:
    claims = build_claims({"org1": ["*"]})
    resources = _build_allowed_resources(METHOD_ARN, claims)

    assert f"{ARN_BASE}/*/*/api/v1/org/org1/*" in resources
    assert f"{ARN_BASE}/*/*/api/v1/org/org1" in resources


@patch("functions.auth.handler.JWTAuthorizer")
def test_lambda_handler_allows_wildcard_org(mock_auth_class) -> None:
    mock_auth = Mock()
    mock_auth_class.return_value = mock_auth

    claims = build_claims({"org1": ["*"]}, sub="sub-1")
    mock_auth.validate_token.return_value = claims

    # Create a proper event dictionary that can be converted to APIGatewayAuthorizerRequestEvent
    event = {
        "authorizationToken": "Bearer valid",
        "methodArn": METHOD_ARN
    }

    policy = lambda_handler(event, Mock())

    assert policy is not None
    assert policy["principalId"] == "sub-1"
    stmt = policy["policyDocument"]["Statement"][0]
    assert stmt["Effect"] == "Allow"

    res = stmt["Resource"]
    # With multiple resources, Resource is a list
    assert isinstance(res, list)
    assert f"{ARN_BASE}/*/*/api/v1/org/org1/*" in res
    assert f"{ARN_BASE}/*/*/api/v1/org/org1" in res


def test_build_allowed_resources_explicit_sites() -> None:
    claims = build_claims({"org2": ["siteA", "siteB"]})
    resources = _build_allowed_resources(METHOD_ARN, claims)

    # org-level
    assert f"{ARN_BASE}/*/*/api/v1/org/org2" in resources
    assert f"{ARN_BASE}/*/*/api/v1/org/org2/schemas/*" in resources
    assert f"{ARN_BASE}/*/*/api/v1/org/org2/use-cases" in resources
    assert f"{ARN_BASE}/*/*/api/v1/org/org2/use-cases/*" in resources
    assert f"{ARN_BASE}/*/*/api/v1/org/org2/installations" in resources
    assert f"{ARN_BASE}/*/*/api/v1/org/org2/installations/*" in resources

    # site-level
    assert f"{ARN_BASE}/*/*/api/v1/org/org2/sites/siteA/*" in resources
    assert f"{ARN_BASE}/*/*/api/v1/org/org2/sites/siteB/*" in resources


@patch("functions.auth.handler.JWTAuthorizer")
def test_lambda_handler_denies_when_no_orgs(mock_auth_class) -> None:
    mock_auth = Mock()
    mock_auth_class.return_value = mock_auth

    claims = build_claims({})
    mock_auth.validate_token.return_value = claims

    # Create a proper event dictionary that can be converted to APIGatewayAuthorizerRequestEvent
    event = {
        "authorizationToken": "Bearer valid",
        "methodArn": METHOD_ARN
    }

    policy = lambda_handler(event, Mock())

    assert policy is not None
    assert policy["principalId"] == "unauthorized"
    stmt = policy["policyDocument"]["Statement"][0]
    assert stmt["Effect"] == "Deny"
    assert stmt["Resource"] == METHOD_ARN

