import os
import warnings
from collections.abc import Iterator

import boto3
import pytest
from aws_lambda_powertools.utilities.typing import LambdaContext
from botocore.exceptions import ClientError
from localstack_utils.localstack import startup_localstack, stop_localstack

from eyecue_things_api.context import Context
from tests.util import (
    ServerFactory,
    TrackerFactory,
    create_dynamodb_table,
    delete_dynamodb_table,
)

# Set JWT environment variables for auth tests before any imports happen
os.environ.setdefault('API_GATEWAY_AUTHORIZER_JWT_DOMAIN', 'https://test.auth0.com/')
os.environ.setdefault('API_GATEWAY_AUTHORIZER_JWT_AUDIENCE', '["https://api.test.com", "https://test.auth0.com/userinfo"]')

LOCALSTACK_ENDPOINT_URL = "http://localhost:4566"


@pytest.fixture(autouse=True, scope="session")
def setup_localstack() -> Iterator[None]:  # pragma: no cover
    """Start and stop LocalStack for the entire test session."""
    localstack_already_running = False

    try:
        startup_localstack()
    except TypeError as e:
        # NOTE:(Campbell): This error is raised when LocalStack is already running
        # so we only want to raise it if it's not this error.
        if str(e) != "exceptions must derive from BaseException":
            raise e

        localstack_already_running = True
        warnings.warn(
            UserWarning(
                "LocalStack is already running and will not "
                "be stopped at the end of this test session."
            ),
            stacklevel=2,
        )

    yield

    # Only stop LocalStack if it was started in this session
    if not localstack_already_running:
        stop_localstack()


@pytest.fixture
def execution_context(
    request: pytest.FixtureRequest,
) -> Iterator[Context]:  # pragma: no cover
    # Give each test a unique table name
    table_name = f"pytest-{request.node.name.replace('_', '-')}"
    session = boto3.Session(
        aws_access_key_id="test",
        aws_secret_access_key="test",
        region_name="us-east-1",
    )
    context = Context(
        boto3_session=session,
        things_shadow_table_name=table_name,
        _endpoint_url=LOCALSTACK_ENDPOINT_URL,
    )

    try:
        create_dynamodb_table(table_name, context.dynamodb)
    except ClientError as e:
        if e.response["Error"]["Code"] != "ResourceInUseException":
            raise

        # Table already exists so we recreate it
        delete_dynamodb_table(context.things_shadow_table)
        create_dynamodb_table(table_name, context.dynamodb)

    yield context

    delete_dynamodb_table(context.things_shadow_table)


@pytest.fixture
def lambda_context() -> LambdaContext:
    """Fixture for a Lambda context.

    Taken from here:
    https://docs.powertools.aws.dev/lambda/python/3.8.0/core/logger/#testing-your-code

    """

    class TestLambdaContext(LambdaContext):
        _function_name: str = "test"
        _memory_limit_in_mb: int = 128
        _invoked_function_arn: str = "arn:aws:lambda:eu-west-1:809313241:function:test"
        _aws_request_id: str = "52fdfc07-2182-154f-163f-5f0f9a621d72"

    return TestLambdaContext()


@pytest.fixture
def tracker_factory() -> TrackerFactory:
    return TrackerFactory()


@pytest.fixture
def server_factory() -> ServerFactory:
    return ServerFactory()
